[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js": "20", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js": "21", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js": "22", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js": "23", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js": "24", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js": "25", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js": "26", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js": "27", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js": "28", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js": "29"}, {"size": 408, "mtime": 1751951658003, "results": "30", "hashOfConfig": "31"}, {"size": 12441, "mtime": 1752115964646, "results": "32", "hashOfConfig": "31"}, {"size": 34451, "mtime": 1752115922868, "results": "33", "hashOfConfig": "31"}, {"size": 1212, "mtime": 1751873051207, "results": "34", "hashOfConfig": "31"}, {"size": 3250, "mtime": 1751953679420, "results": "35", "hashOfConfig": "31"}, {"size": 4791, "mtime": 1751960448046, "results": "36", "hashOfConfig": "31"}, {"size": 3994, "mtime": 1752113564124, "results": "37", "hashOfConfig": "31"}, {"size": 4610, "mtime": 1751946228349, "results": "38", "hashOfConfig": "31"}, {"size": 5273, "mtime": 1751960463052, "results": "39", "hashOfConfig": "31"}, {"size": 8598, "mtime": 1751939997191, "results": "40", "hashOfConfig": "31"}, {"size": 4230, "mtime": 1751940026705, "results": "41", "hashOfConfig": "31"}, {"size": 1735, "mtime": 1751940008151, "results": "42", "hashOfConfig": "31"}, {"size": 4711, "mtime": 1751946191614, "results": "43", "hashOfConfig": "31"}, {"size": 4495, "mtime": 1751940037703, "results": "44", "hashOfConfig": "31"}, {"size": 3655, "mtime": 1751948557098, "results": "45", "hashOfConfig": "31"}, {"size": 4863, "mtime": 1751950041198, "results": "46", "hashOfConfig": "31"}, {"size": 3929, "mtime": 1751946465633, "results": "47", "hashOfConfig": "31"}, {"size": 4027, "mtime": 1751945104895, "results": "48", "hashOfConfig": "31"}, {"size": 3679, "mtime": 1751944188070, "results": "49", "hashOfConfig": "31"}, {"size": 4387, "mtime": 1752112003947, "results": "50", "hashOfConfig": "31"}, {"size": 3678, "mtime": 1751967553714, "results": "51", "hashOfConfig": "31"}, {"size": 4650, "mtime": 1752111918233, "results": "52", "hashOfConfig": "31"}, {"size": 5979, "mtime": 1752111934504, "results": "53", "hashOfConfig": "31"}, {"size": 4355, "mtime": 1752111904775, "results": "54", "hashOfConfig": "31"}, {"size": 6983, "mtime": 1752115841378, "results": "55", "hashOfConfig": "31"}, {"size": 7638, "mtime": 1752115874068, "results": "56", "hashOfConfig": "31"}, {"size": 4798, "mtime": 1752115812224, "results": "57", "hashOfConfig": "31"}, {"size": 3286, "mtime": 1752114312287, "results": "58", "hashOfConfig": "31"}, {"size": 6196, "mtime": 1752114298217, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ji7irk", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["147", "148", "149", "150", "151", "152", "153", "154", "155"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["156"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js", ["157"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js", ["158"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js", ["159"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js", ["160"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js", ["161"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js", ["162"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js", [], [], {"ruleId": "163", "severity": 1, "message": "164", "line": 138, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 138, "endColumn": 18}, {"ruleId": "163", "severity": 1, "message": "167", "line": 140, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 140, "endColumn": 23}, {"ruleId": "163", "severity": 1, "message": "168", "line": 251, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 251, "endColumn": 17}, {"ruleId": "163", "severity": 1, "message": "164", "line": 395, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 395, "endColumn": 18}, {"ruleId": "163", "severity": 1, "message": "167", "line": 397, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 397, "endColumn": 23}, {"ruleId": "163", "severity": 1, "message": "168", "line": 508, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 508, "endColumn": 17}, {"ruleId": "163", "severity": 1, "message": "164", "line": 652, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 652, "endColumn": 18}, {"ruleId": "163", "severity": 1, "message": "167", "line": 654, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 654, "endColumn": 23}, {"ruleId": "163", "severity": 1, "message": "168", "line": 765, "column": 7, "nodeType": "165", "messageId": "166", "endLine": 765, "endColumn": 17}, {"ruleId": "169", "severity": 1, "message": "170", "line": 49, "column": 8, "nodeType": "171", "endLine": 49, "endColumn": 10, "suggestions": "172"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 3, "column": 44, "nodeType": "175", "messageId": "176", "endLine": 3, "endColumn": 50}, {"ruleId": "173", "severity": 1, "message": "177", "line": 2, "column": 44, "nodeType": "175", "messageId": "176", "endLine": 2, "endColumn": 49}, {"ruleId": "173", "severity": 1, "message": "177", "line": 2, "column": 44, "nodeType": "175", "messageId": "176", "endLine": 2, "endColumn": 49}, {"ruleId": "173", "severity": 1, "message": "177", "line": 2, "column": 44, "nodeType": "175", "messageId": "176", "endLine": 2, "endColumn": 49}, {"ruleId": "173", "severity": 1, "message": "177", "line": 2, "column": 44, "nodeType": "175", "messageId": "176", "endLine": 2, "endColumn": 49}, {"ruleId": "173", "severity": 1, "message": "177", "line": 2, "column": 44, "nodeType": "175", "messageId": "176", "endLine": 2, "endColumn": 49}, "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "Duplicate key 'batch_id'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["178"], "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'Badge' is defined but never used.", {"desc": "179", "fix": "180"}, "Update the dependencies array to be: [t]", {"range": "181", "text": "182"}, [1977, 1979], "[t]"]