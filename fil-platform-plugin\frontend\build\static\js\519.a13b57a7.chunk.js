"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[519],{1072:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...o}=e;const c=(0,d.oU)(r,"row"),i=(0,d.gy)(),f=(0,d.Jm)(),x=`${c}-cols`,u=[];return i.forEach(e=>{const s=o[e];let r;delete o[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&u.push(`${x}${a}-${r}`)}),(0,n.jsx)(l,{ref:s,...o,className:t()(a,c,...u)})});o.displayName="Row";const c=o},4063:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:l=!1,text:o,className:c,as:i="span",...f}=e;const x=(0,d.oU)(r,"badge");return(0,n.jsx)(i,{ref:s,...f,className:t()(c,x,l&&"rounded-pill",o&&`text-${o}`,a&&`bg-${a}`)})});o.displayName="Badge";const c=o},4196:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:l,bordered:o,borderless:c,hover:i,size:f,variant:x,responsive:u,...h}=e;const m=(0,d.oU)(r,"table"),b=t()(a,m,x&&`${m}-${x}`,f&&`${m}-${f}`,l&&`${m}-${"string"===typeof l?`striped-${l}`:"striped"}`,o&&`${m}-bordered`,c&&`${m}-borderless`,i&&`${m}-hover`),v=(0,n.jsx)("table",{...h,className:b,ref:s});if(u){let e=`${m}-responsive`;return"string"===typeof u&&(e=`${e}-${u}`),(0,n.jsx)("div",{className:e,children:v})}return v});o.displayName="Table";const c=o},5519:(e,s,r)=>{r.r(s),r.d(s,{default:()=>u});var a=r(5043),t=r(3519),l=r(1072),d=r(8602),n=r(8628),o=r(4196),c=r(4063),i=r(4312),f=r(4117),x=r(579);const u=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[u,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:t}=await e.from("user_assets").select("\n                    user_id,\n                    currency_code,\n                    balance_available,\n                    balance_locked,\n                    balance_total,\n                    withdrawn_total,\n                    users (\n                        email,\n                        phone,\n                        role\n                    ),\n                    currencies (\n                        code,\n                        total_supply,\n                        withdrawable\n                    )\n                ").order("balance_total",{ascending:!1});t?console.error("Error fetching customer assets:",t):r(a),h(!1)})()},[]),u?(0,x.jsx)("div",{children:e("loading_customer_assets")}):(0,x.jsxs)(t.A,{children:[(0,x.jsx)("h2",{className:"mb-4",children:e("customer_assets")}),(0,x.jsx)(l.A,{children:(0,x.jsx)(d.A,{children:(0,x.jsx)(n.A,{children:(0,x.jsx)(n.A.Body,{children:(0,x.jsxs)(o.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:e("user_email")}),(0,x.jsx)("th",{children:e("user_phone")}),(0,x.jsx)("th",{children:e("user_role")}),(0,x.jsx)("th",{children:e("currency_code")}),(0,x.jsx)("th",{children:e("balance_available")}),(0,x.jsx)("th",{children:e("balance_locked")}),(0,x.jsx)("th",{children:e("balance_total")}),(0,x.jsx)("th",{children:e("withdrawn_total")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"8",className:"text-center",children:e("no_customer_assets_available")})}):s.map((e,s)=>{var r,a,t,l,d,n,o,i;return(0,x.jsxs)("tr",{children:[(0,x.jsx)("td",{children:(null===(r=e.users)||void 0===r?void 0:r.email)||"-"}),(0,x.jsx)("td",{children:(null===(a=e.users)||void 0===a?void 0:a.phone)||"-"}),(0,x.jsx)("td",{children:(0,x.jsx)(c.A,{bg:"customer"===(null===(t=e.users)||void 0===t?void 0:t.role)?"primary":"secondary",children:(null===(l=e.users)||void 0===l?void 0:l.role)||"-"})}),(0,x.jsx)("td",{children:e.currency_code}),(0,x.jsx)("td",{children:(null===(d=e.balance_available)||void 0===d?void 0:d.toFixed(6))||"0.000000"}),(0,x.jsx)("td",{children:(null===(n=e.balance_locked)||void 0===n?void 0:n.toFixed(6))||"0.000000"}),(0,x.jsx)("td",{children:(null===(o=e.balance_total)||void 0===o?void 0:o.toFixed(6))||"0.000000"}),(0,x.jsx)("td",{children:(null===(i=e.withdrawn_total)||void 0===i?void 0:i.toFixed(6))||"0.000000"})]},`${e.user_id}-${e.currency_code}`)})})]})})})})})]})}},8602:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{const[{className:r,...a},{as:l="div",bsPrefix:o,spans:c}]=function(e){let{as:s,bsPrefix:r,className:a,...l}=e;r=(0,d.oU)(r,"col");const n=(0,d.gy)(),o=(0,d.Jm)(),c=[],i=[];return n.forEach(e=>{const s=l[e];let a,t,d;delete l[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:d}=s):a=s;const n=e!==o?`-${e}`:"";a&&c.push(!0===a?`${r}${n}`:`${r}${n}-${a}`),null!=d&&i.push(`order${n}-${d}`),null!=t&&i.push(`offset${n}-${t}`)}),[{...l,className:t()(a,...c,...i)},{as:s,bsPrefix:r,spans:c}]}(e);return(0,n.jsx)(l,{...a,ref:s,className:t()(r,!c.length&&o)})});o.displayName="Col";const c=o},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,d.oU)(a,"card-body"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});o.displayName="CardBody";const c=o,i=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,d.oU)(a,"card-footer"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});i.displayName="CardFooter";const f=i;var x=r(1778);const u=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:o="div",...c}=e;const i=(0,d.oU)(r,"card-header"),f=(0,l.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,n.jsx)(x.A.Provider,{value:f,children:(0,n.jsx)(o,{ref:s,...c,className:t()(a,i)})})});u.displayName="CardHeader";const h=u,m=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:l,as:o="img",...c}=e;const i=(0,d.oU)(r,"card-img");return(0,n.jsx)(o,{ref:s,className:t()(l?`${i}-${l}`:i,a),...c})});m.displayName="CardImg";const b=m,v=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});v.displayName="CardImgOverlay";const j=v,p=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="a",...o}=e;return a=(0,d.oU)(a,"card-link"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});p.displayName="CardLink";const N=p;var y=r(4488);const $=(0,y.A)("h6"),_=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=$,...o}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});_.displayName="CardSubtitle";const w=_,g=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="p",...o}=e;return a=(0,d.oU)(a,"card-text"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});g.displayName="CardText";const P=g,A=(0,y.A)("h5"),R=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=A,...o}=e;return a=(0,d.oU)(a,"card-title"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});R.displayName="CardTitle";const U=R,C=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:l,text:o,border:i,body:f=!1,children:x,as:u="div",...h}=e;const m=(0,d.oU)(r,"card");return(0,n.jsx)(u,{ref:s,...h,className:t()(a,m,l&&`bg-${l}`,o&&`text-${o}`,i&&`border-${i}`),children:f?(0,n.jsx)(c,{children:x}):x})});C.displayName="Card";const k=Object.assign(C,{Img:b,Title:U,Subtitle:w,Body:c,Link:N,Text:P,Header:h,Footer:f,ImgOverlay:j})}}]);
//# sourceMappingURL=519.a13b57a7.chunk.js.map