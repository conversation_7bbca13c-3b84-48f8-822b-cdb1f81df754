import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MakerFacilities = () => {
    const { t } = useTranslation();
    const [facilities, setFacilities] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchFacilities = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch facilities associated with products from this maker
            const { data, error } = await supabase
                .from('facilities')
                .select(`
                    id,
                    name,
                    created_at,
                    updated_at
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching facilities:', error);
            } else {
                setFacilities(data);
            }
            setLoading(false);
        };

        fetchFacilities();
    }, []);

    if (loading) {
        return <div>{t('loading_facilities')}</div>;
    }

    return (
        <Container>
                <h2 className="mb-4">{t('all_facilities')}</h2>
                <Row>
                    <Col>
                        <Card>
                            <Card.Body>
                                <Table striped bordered hover responsive>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>{t('name')}</th>
                                            <th>{t('created_at')}</th>
                                            <th>{t('agent')}</th>
                                            <th>{t('actions')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {facilities.length === 0 ? (
                                            <tr>
                                                <td colSpan="9" className="text-center">{t('no_facilities_available')}</td>
                                            </tr>
                                        ) : (
                                            facilities.map(facility => (
                                                <tr key={facility.id}>
                                                    <td>{facility.id}</td>
                                                    <td>{facility.name}</td>
                                                    <td>{new Date(facility.created_at).toLocaleString()}</td>
                                                    <td>{new Date(facility.updated_at).toLocaleString()}</td>
                                                    <td>Edit/Delete</td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </Table>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
        </Container>
    );
};

export default MakerFacilities;
