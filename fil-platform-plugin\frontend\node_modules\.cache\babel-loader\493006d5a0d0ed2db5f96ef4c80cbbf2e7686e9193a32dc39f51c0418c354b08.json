{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CoinBatches=()=>{const{t}=useTranslation();const[batches,setBatches]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchBatches=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch distribution batches with related information\nconst{data,error}=await supabase.from('distribution_batches').select(`\n                    id,\n                    maker_id,\n                    agent_id,\n                    currency_code,\n                    product_id,\n                    shares,\n                    batch_amount,\n                    per_share_amount,\n                    status,\n                    created_at,\n                    distributed_at,\n                    maker:maker_profiles!maker_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    ),\n                    agent:agent_profiles!agent_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    ),\n                    product:products!product_id (\n                        name,\n                        category\n                    ),\n                    currency:currencies!currency_code (\n                        code\n                    )\n                `).order('created_at',{ascending:false});if(error){console.error('Error fetching distribution batches:',error);}else{setBatches(data);}setLoading(false);};fetchBatches();},[]);const getStatusColor=status=>{switch(status){case'pending':return'warning';case'distributed':return'success';case'failed':return'danger';case'cancelled':return'secondary';default:return'primary';}};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_batches')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('coin_batches')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('batch_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('maker')}),/*#__PURE__*/_jsx(\"th\",{children:t('agent')}),/*#__PURE__*/_jsx(\"th\",{children:t('product_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('currency')}),/*#__PURE__*/_jsx(\"th\",{children:t('shares')}),/*#__PURE__*/_jsx(\"th\",{children:t('batch_amount')}),/*#__PURE__*/_jsx(\"th\",{children:t('per_share_amount')}),/*#__PURE__*/_jsx(\"th\",{children:t('status')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')}),/*#__PURE__*/_jsx(\"th\",{children:t('distributed_at')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:batches.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"11\",className:\"text-center\",children:t('no_batches_available')})}):batches.map(batch=>{var _batch$maker,_batch$maker$users,_batch$agent,_batch$agent$users,_batch$product;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{children:[batch.id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{children:((_batch$maker=batch.maker)===null||_batch$maker===void 0?void 0:(_batch$maker$users=_batch$maker.users)===null||_batch$maker$users===void 0?void 0:_batch$maker$users.email)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_batch$agent=batch.agent)===null||_batch$agent===void 0?void 0:(_batch$agent$users=_batch$agent.users)===null||_batch$agent$users===void 0?void 0:_batch$agent$users.email)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_batch$product=batch.product)===null||_batch$product===void 0?void 0:_batch$product.name)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:batch.currency_code||'-'}),/*#__PURE__*/_jsx(\"td\",{children:batch.shares?Number(batch.shares).toFixed(2):'0'}),/*#__PURE__*/_jsxs(\"td\",{children:[batch.batch_amount?Number(batch.batch_amount).toFixed(6):'0',\" \",batch.currency_code]}),/*#__PURE__*/_jsxs(\"td\",{children:[batch.per_share_amount?Number(batch.per_share_amount).toFixed(6):'0',\" \",batch.currency_code]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:getStatusColor(batch.status),children:batch.status||'unknown'})}),/*#__PURE__*/_jsx(\"td\",{children:new Date(batch.created_at).toLocaleString()}),/*#__PURE__*/_jsx(\"td\",{children:batch.distributed_at?new Date(batch.distributed_at).toLocaleString():'-'})]},batch.id);})})]})})})})})]});};export default CoinBatches;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "CoinBatches", "t", "batches", "setBatches", "loading", "setLoading", "fetchBatches", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "getStatusColor", "status", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "batch", "_batch$maker", "_batch$maker$users", "_batch$agent", "_batch$agent$users", "_batch$product", "id", "substring", "maker", "users", "email", "agent", "product", "name", "currency_code", "shares", "Number", "toFixed", "batch_amount", "per_share_amount", "bg", "Date", "created_at", "toLocaleString", "distributed_at"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/CoinBatches.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst CoinBatches = () => {\n    const { t } = useTranslation();\n    const [batches, setBatches] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchBatches = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch distribution batches with related information\n            const { data, error } = await supabase\n                .from('distribution_batches')\n                .select(`\n                    id,\n                    maker_id,\n                    agent_id,\n                    currency_code,\n                    product_id,\n                    shares,\n                    batch_amount,\n                    per_share_amount,\n                    status,\n                    created_at,\n                    distributed_at,\n                    maker:maker_profiles!maker_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    ),\n                    agent:agent_profiles!agent_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    ),\n                    product:products!product_id (\n                        name,\n                        category\n                    ),\n                    currency:currencies!currency_code (\n                        code\n                    )\n                `)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching distribution batches:', error);\n            } else {\n                setBatches(data);\n            }\n            setLoading(false);\n        };\n\n        fetchBatches();\n    }, []);\n\n    const getStatusColor = (status) => {\n        switch (status) {\n            case 'pending':\n                return 'warning';\n            case 'distributed':\n                return 'success';\n            case 'failed':\n                return 'danger';\n            case 'cancelled':\n                return 'secondary';\n            default:\n                return 'primary';\n        }\n    };\n\n    if (loading) {\n        return <div>{t('loading_batches')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('coin_batches')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('batch_id')}</th>\n                                        <th>{t('maker')}</th>\n                                        <th>{t('agent')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('currency')}</th>\n                                        <th>{t('shares')}</th>\n                                        <th>{t('batch_amount')}</th>\n                                        <th>{t('per_share_amount')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('created_at')}</th>\n                                        <th>{t('distributed_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {batches.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"11\" className=\"text-center\">{t('no_batches_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        batches.map((batch) => (\n                                            <tr key={batch.id}>\n                                                <td>{batch.id.substring(0, 8)}...</td>\n                                                <td>{batch.maker?.users?.email || '-'}</td>\n                                                <td>{batch.agent?.users?.email || '-'}</td>\n                                                <td>{batch.product?.name || '-'}</td>\n                                                <td>{batch.currency_code || '-'}</td>\n                                                <td>{batch.shares ? Number(batch.shares).toFixed(2) : '0'}</td>\n                                                <td>{batch.batch_amount ? Number(batch.batch_amount).toFixed(6) : '0'} {batch.currency_code}</td>\n                                                <td>{batch.per_share_amount ? Number(batch.per_share_amount).toFixed(6) : '0'} {batch.currency_code}</td>\n                                                <td>\n                                                    <Badge bg={getStatusColor(batch.status)}>\n                                                        {batch.status || 'unknown'}\n                                                    </Badge>\n                                                </td>\n                                                <td>{new Date(batch.created_at).toLocaleString()}</td>\n                                                <td>{batch.distributed_at ? new Date(batch.distributed_at).toLocaleString() : '-'}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default CoinBatches;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAChE,CAAC,IAAM,CACHT,UAAU,CAACK,IAAI,CAAC,CACpB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,YAAY,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAY,cAAc,CAAIC,MAAM,EAAK,CAC/B,OAAQA,MAAM,EACV,IAAK,SAAS,CACV,MAAO,SAAS,CACpB,IAAK,aAAa,CACd,MAAO,SAAS,CACpB,IAAK,QAAQ,CACT,MAAO,QAAQ,CACnB,IAAK,WAAW,CACZ,MAAO,WAAW,CACtB,QACI,MAAO,SAAS,CACxB,CACJ,CAAC,CAED,GAAIf,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,iBAAiB,CAAC,CAAM,CAAC,CAC5C,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAAgC,QAAA,eACNvB,IAAA,OAAIwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEnB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC7CJ,IAAA,CAACR,GAAG,EAAA+B,QAAA,cACAvB,IAAA,CAACP,GAAG,EAAA8B,QAAA,cACAvB,IAAA,CAACN,IAAI,EAAA6B,QAAA,cACDvB,IAAA,CAACN,IAAI,CAAC+B,IAAI,EAAAF,QAAA,cACNrB,KAAA,CAACP,KAAK,EAAC+B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCvB,IAAA,UAAAuB,QAAA,cACIrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,cAChCJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,EAC9B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAuB,QAAA,CACKlB,OAAO,CAACyB,MAAM,GAAK,CAAC,cACjB9B,IAAA,OAAAuB,QAAA,cACIvB,IAAA,OAAI+B,OAAO,CAAC,IAAI,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEnB,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,CACzE,CAAC,CAELC,OAAO,CAAC2B,GAAG,CAAEC,KAAK,OAAAC,YAAA,CAAAC,kBAAA,CAAAC,YAAA,CAAAC,kBAAA,CAAAC,cAAA,oBACdpC,KAAA,OAAAqB,QAAA,eACIrB,KAAA,OAAAqB,QAAA,EAAKU,KAAK,CAACM,EAAE,CAACC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KAAG,EAAI,CAAC,cACtCxC,IAAA,OAAAuB,QAAA,CAAK,EAAAW,YAAA,CAAAD,KAAK,CAACQ,KAAK,UAAAP,YAAA,kBAAAC,kBAAA,CAAXD,YAAA,CAAaQ,KAAK,UAAAP,kBAAA,iBAAlBA,kBAAA,CAAoBQ,KAAK,GAAI,GAAG,CAAK,CAAC,cAC3C3C,IAAA,OAAAuB,QAAA,CAAK,EAAAa,YAAA,CAAAH,KAAK,CAACW,KAAK,UAAAR,YAAA,kBAAAC,kBAAA,CAAXD,YAAA,CAAaM,KAAK,UAAAL,kBAAA,iBAAlBA,kBAAA,CAAoBM,KAAK,GAAI,GAAG,CAAK,CAAC,cAC3C3C,IAAA,OAAAuB,QAAA,CAAK,EAAAe,cAAA,CAAAL,KAAK,CAACY,OAAO,UAAAP,cAAA,iBAAbA,cAAA,CAAeQ,IAAI,GAAI,GAAG,CAAK,CAAC,cACrC9C,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACc,aAAa,EAAI,GAAG,CAAK,CAAC,cACrC/C,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACe,MAAM,CAAGC,MAAM,CAAChB,KAAK,CAACe,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAK,CAAC,cAC/DhD,KAAA,OAAAqB,QAAA,EAAKU,KAAK,CAACkB,YAAY,CAAGF,MAAM,CAAChB,KAAK,CAACkB,YAAY,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,GAAC,CAACjB,KAAK,CAACc,aAAa,EAAK,CAAC,cACjG7C,KAAA,OAAAqB,QAAA,EAAKU,KAAK,CAACmB,gBAAgB,CAAGH,MAAM,CAAChB,KAAK,CAACmB,gBAAgB,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,GAAC,CAACjB,KAAK,CAACc,aAAa,EAAK,CAAC,cACzG/C,IAAA,OAAAuB,QAAA,cACIvB,IAAA,CAACJ,KAAK,EAACyD,EAAE,CAAEhC,cAAc,CAACY,KAAK,CAACX,MAAM,CAAE,CAAAC,QAAA,CACnCU,KAAK,CAACX,MAAM,EAAI,SAAS,CACvB,CAAC,CACR,CAAC,cACLtB,IAAA,OAAAuB,QAAA,CAAK,GAAI,CAAA+B,IAAI,CAACrB,KAAK,CAACsB,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,cACtDxD,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACwB,cAAc,CAAG,GAAI,CAAAH,IAAI,CAACrB,KAAK,CAACwB,cAAc,CAAC,CAACD,cAAc,CAAC,CAAC,CAAG,GAAG,CAAK,CAAC,GAflFvB,KAAK,CAACM,EAgBX,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAApC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}