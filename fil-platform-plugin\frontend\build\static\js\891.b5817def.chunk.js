"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[891],{1072:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),c=s(579);const i=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:n="div",...i}=e;const l=(0,d.oU)(s,"row"),o=(0,d.gy)(),u=(0,d.Jm)(),h=`${l}-cols`,f=[];return o.forEach(e=>{const r=i[e];let s;delete i[e],null!=r&&"object"===typeof r?({cols:s}=r):s=r;const a=e!==u?`-${e}`:"";null!=s&&f.push(`${h}${a}-${s}`)}),(0,c.jsx)(n,{ref:r,...i,className:t()(a,l,...f)})});i.displayName="Row";const l=i},4063:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),c=s(579);const i=n.forwardRef((e,r)=>{let{bsPrefix:s,bg:a="primary",pill:n=!1,text:i,className:l,as:o="span",...u}=e;const h=(0,d.oU)(s,"badge");return(0,c.jsx)(o,{ref:r,...u,className:t()(l,h,n&&"rounded-pill",i&&`text-${i}`,a&&`bg-${a}`)})});i.displayName="Badge";const l=i},4196:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),c=s(579);const i=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,striped:n,bordered:i,borderless:l,hover:o,size:u,variant:h,responsive:f,...m}=e;const x=(0,d.oU)(s,"table"),b=t()(a,x,h&&`${x}-${h}`,u&&`${x}-${u}`,n&&`${x}-${"string"===typeof n?`striped-${n}`:"striped"}`,i&&`${x}-bordered`,l&&`${x}-borderless`,o&&`${x}-hover`),j=(0,c.jsx)("table",{...m,className:b,ref:r});if(f){let e=`${x}-responsive`;return"string"===typeof f&&(e=`${e}-${f}`),(0,c.jsx)("div",{className:e,children:j})}return j});i.displayName="Table";const l=i},6891:(e,r,s)=>{s.r(r),s.d(r,{default:()=>f});var a=s(5043),t=s(3519),n=s(1072),d=s(8602),c=s(8628),i=s(4196),l=s(4063),o=s(4312),u=s(4117),h=s(579);const f=()=>{const{t:e}=(0,u.Bd)(),[r,s]=(0,a.useState)([]),[f,m]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{const e=(0,o.b)();if(!e)return;m(!0);const{data:{user:r}}=await e.auth.getUser();if(!r)return void m(!1);const{data:a,error:t}=await e.from("distribution_batches").select("\n                    id,\n                    maker_id,\n                    agent_id,\n                    currency_code,\n                    product_id,\n                    shares,\n                    batch_amount,\n                    per_share_amount,\n                    status,\n                    created_at,\n                    distributed_at,\n                    maker:maker_profiles!maker_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    ),\n                    agent:agent_profiles!agent_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    ),\n                    product:products!product_id (\n                        name,\n                        category\n                    ),\n                    currency:currencies!currency_code (\n                        code\n                    )\n                ").order("created_at",{ascending:!1});t?console.error("Error fetching distribution batches:",t):s(a),m(!1)})()},[]);const x=e=>{switch(e){case"pending":return"warning";case"distributed":return"success";case"failed":return"danger";case"cancelled":return"secondary";default:return"primary"}};return f?(0,h.jsx)("div",{children:e("loading_batches")}):(0,h.jsxs)(t.A,{children:[(0,h.jsx)("h2",{className:"mb-4",children:e("coin_batches")}),(0,h.jsx)(n.A,{children:(0,h.jsx)(d.A,{children:(0,h.jsx)(c.A,{children:(0,h.jsx)(c.A.Body,{children:(0,h.jsxs)(i.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,h.jsx)("thead",{children:(0,h.jsxs)("tr",{children:[(0,h.jsx)("th",{children:e("batch_id")}),(0,h.jsx)("th",{children:e("maker")}),(0,h.jsx)("th",{children:e("agent")}),(0,h.jsx)("th",{children:e("product_name")}),(0,h.jsx)("th",{children:e("currency")}),(0,h.jsx)("th",{children:e("shares")}),(0,h.jsx)("th",{children:e("batch_amount")}),(0,h.jsx)("th",{children:e("per_share_amount")}),(0,h.jsx)("th",{children:e("status")}),(0,h.jsx)("th",{children:e("created_at")}),(0,h.jsx)("th",{children:e("distributed_at")})]})}),(0,h.jsx)("tbody",{children:0===r.length?(0,h.jsx)("tr",{children:(0,h.jsx)("td",{colSpan:"11",className:"text-center",children:e("no_batches_available")})}):r.map(e=>{var r,s,a,t,n;return(0,h.jsxs)("tr",{children:[(0,h.jsxs)("td",{children:[e.id.substring(0,8),"..."]}),(0,h.jsx)("td",{children:(null===(r=e.maker)||void 0===r||null===(s=r.users)||void 0===s?void 0:s.email)||"-"}),(0,h.jsx)("td",{children:(null===(a=e.agent)||void 0===a||null===(t=a.users)||void 0===t?void 0:t.email)||"-"}),(0,h.jsx)("td",{children:(null===(n=e.product)||void 0===n?void 0:n.name)||"-"}),(0,h.jsx)("td",{children:e.currency_code||"-"}),(0,h.jsx)("td",{children:e.shares?Number(e.shares).toFixed(2):"0"}),(0,h.jsxs)("td",{children:[e.batch_amount?Number(e.batch_amount).toFixed(6):"0"," ",e.currency_code]}),(0,h.jsxs)("td",{children:[e.per_share_amount?Number(e.per_share_amount).toFixed(6):"0"," ",e.currency_code]}),(0,h.jsx)("td",{children:(0,h.jsx)(l.A,{bg:x(e.status),children:e.status||"unknown"})}),(0,h.jsx)("td",{children:new Date(e.created_at).toLocaleString()}),(0,h.jsx)("td",{children:e.distributed_at?new Date(e.distributed_at).toLocaleString():"-"})]},e.id)})})]})})})})})]})}},8602:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),c=s(579);const i=n.forwardRef((e,r)=>{const[{className:s,...a},{as:n="div",bsPrefix:i,spans:l}]=function(e){let{as:r,bsPrefix:s,className:a,...n}=e;s=(0,d.oU)(s,"col");const c=(0,d.gy)(),i=(0,d.Jm)(),l=[],o=[];return c.forEach(e=>{const r=n[e];let a,t,d;delete n[e],"object"===typeof r&&null!=r?({span:a,offset:t,order:d}=r):a=r;const c=e!==i?`-${e}`:"";a&&l.push(!0===a?`${s}${c}`:`${s}${c}-${a}`),null!=d&&o.push(`order${c}-${d}`),null!=t&&o.push(`offset${c}-${t}`)}),[{...n,className:t()(a,...l,...o)},{as:r,bsPrefix:s,spans:l}]}(e);return(0,c.jsx)(n,{...a,ref:r,className:t()(s,!l.length&&i)})});i.displayName="Col";const l=i},8628:(e,r,s)=>{s.d(r,{A:()=>C});var a=s(8139),t=s.n(a),n=s(5043),d=s(7852),c=s(579);const i=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="div",...i}=e;return a=(0,d.oU)(a,"card-body"),(0,c.jsx)(n,{ref:r,className:t()(s,a),...i})});i.displayName="CardBody";const l=i,o=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="div",...i}=e;return a=(0,d.oU)(a,"card-footer"),(0,c.jsx)(n,{ref:r,className:t()(s,a),...i})});o.displayName="CardFooter";const u=o;var h=s(1778);const f=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,as:i="div",...l}=e;const o=(0,d.oU)(s,"card-header"),u=(0,n.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,c.jsx)(h.A.Provider,{value:u,children:(0,c.jsx)(i,{ref:r,...l,className:t()(a,o)})})});f.displayName="CardHeader";const m=f,x=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,variant:n,as:i="img",...l}=e;const o=(0,d.oU)(s,"card-img");return(0,c.jsx)(i,{ref:r,className:t()(n?`${o}-${n}`:o,a),...l})});x.displayName="CardImg";const b=x,j=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="div",...i}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,c.jsx)(n,{ref:r,className:t()(s,a),...i})});j.displayName="CardImgOverlay";const p=j,N=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="a",...i}=e;return a=(0,d.oU)(a,"card-link"),(0,c.jsx)(n,{ref:r,className:t()(s,a),...i})});N.displayName="CardLink";const _=N;var v=s(4488);const y=(0,v.A)("h6"),g=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n=y,...i}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,c.jsx)(n,{ref:r,className:t()(s,a),...i})});g.displayName="CardSubtitle";const $=g,w=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n="p",...i}=e;return a=(0,d.oU)(a,"card-text"),(0,c.jsx)(n,{ref:r,className:t()(s,a),...i})});w.displayName="CardText";const P=w,A=(0,v.A)("h5"),R=n.forwardRef((e,r)=>{let{className:s,bsPrefix:a,as:n=A,...i}=e;return a=(0,d.oU)(a,"card-title"),(0,c.jsx)(n,{ref:r,className:t()(s,a),...i})});R.displayName="CardTitle";const U=R,k=n.forwardRef((e,r)=>{let{bsPrefix:s,className:a,bg:n,text:i,border:o,body:u=!1,children:h,as:f="div",...m}=e;const x=(0,d.oU)(s,"card");return(0,c.jsx)(f,{ref:r,...m,className:t()(a,x,n&&`bg-${n}`,i&&`text-${i}`,o&&`border-${o}`),children:u?(0,c.jsx)(l,{children:h}):h})});k.displayName="Card";const C=Object.assign(k,{Img:b,Title:U,Subtitle:$,Body:l,Link:_,Text:P,Header:m,Footer:u,ImgOverlay:p})}}]);
//# sourceMappingURL=891.b5817def.chunk.js.map