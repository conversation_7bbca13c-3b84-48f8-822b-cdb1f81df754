import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const CustomerAssets = () => {
    const { t } = useTranslation();
    const [assets, setAssets] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchAssets = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch user assets with user information
            const { data, error } = await supabase
                .from('user_assets')
                .select(`
                    user_id,
                    currency_code,
                    balance_available,
                    balance_locked,
                    balance_total,
                    withdrawn_total,
                    users (
                        email,
                        phone,
                        role
                    ),
                    currencies (
                        code,
                        total_supply,
                        withdrawable
                    )
                `)
                .order('balance_total', { ascending: false });

            if (error) {
                console.error('Error fetching customer assets:', error);
            } else {
                setAssets(data);
            }
            setLoading(false);
        };

        fetchAssets();
    }, []);

    if (loading) {
        return <div>{t('loading_customer_assets')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('customer_assets')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('user_email')}</th>
                                        <th>{t('user_phone')}</th>
                                        <th>{t('user_role')}</th>
                                        <th>{t('currency_code')}</th>
                                        <th>{t('available_balance')}</th>
                                        <th>{t('locked_balance')}</th>
                                        <th>{t('balance_total')}</th>
                                        <th>{t('withdrawn_total')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {assets.length === 0 ? (
                                        <tr>
                                            <td colSpan="8" className="text-center">{t('no_customer_assets_available')}</td>
                                        </tr>
                                    ) : (
                                        assets.map((asset, index) => (
                                            <tr key={`${asset.user_id}-${asset.currency_code}`}>
                                                <td>{asset.users?.email || '-'}</td>
                                                <td>{asset.users?.phone || '-'}</td>
                                                <td>
                                                    <Badge bg={asset.users?.role === 'customer' ? 'primary' : 'secondary'}>
                                                        {asset.users?.role || '-'}
                                                    </Badge>
                                                </td>
                                                <td>{asset.currency_code}</td>
                                                <td>{asset.balance_available?.toFixed(6) || '0.000000'}</td>
                                                <td>{asset.balance_locked?.toFixed(6) || '0.000000'}</td>
                                                <td>{asset.balance_total?.toFixed(6) || '0.000000'}</td>
                                                <td>{asset.withdrawn_total?.toFixed(6) || '0.000000'}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default CustomerAssets;
