{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NetworkStats=()=>{const{t}=useTranslation();const[stats,setStats]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchStats=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch network stats\nconst{data,error}=await supabase.from('network_stats').select(`\n                    stat_date,\n                    blockchain_height,\n                    fil_per_tib\n                `).order('stat_date',{ascending:false});if(error){console.error('Error fetching network stats:',error);}else{setStats(data);}setLoading(false);};fetchStats();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_network_stats')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('network_stats')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('stat_date')}),/*#__PURE__*/_jsx(\"th\",{children:t('blockchain_height')}),/*#__PURE__*/_jsx(\"th\",{children:t('fil_per_tib')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:stats.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"3\",className:\"text-center\",children:t('no_network_stats_available')})}):stats.map((stat,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:new Date(stat.stat_date).toLocaleDateString()}),/*#__PURE__*/_jsx(\"td\",{children:stat.blockchain_height?stat.blockchain_height.toLocaleString():'-'}),/*#__PURE__*/_jsxs(\"td\",{children:[stat.fil_per_tib?Number(stat.fil_per_tib).toFixed(8):'0',\" FIL/TiB\"]})]},`${stat.stat_date}`))})]})})})})})]});};export default NetworkStats;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "NetworkStats", "t", "stats", "setStats", "loading", "setLoading", "fetchStats", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "stat", "index", "Date", "stat_date", "toLocaleDateString", "blockchain_height", "toLocaleString", "fil_per_tib", "Number", "toFixed"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/NetworkStats.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst NetworkStats = () => {\n    const { t } = useTranslation();\n    const [stats, setStats] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchStats = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch network stats\n            const { data, error } = await supabase\n                .from('network_stats')\n                .select(`\n                    stat_date,\n                    blockchain_height,\n                    fil_per_tib\n                `)\n                .order('stat_date', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching network stats:', error);\n            } else {\n                setStats(data);\n            }\n            setLoading(false);\n        };\n\n        fetchStats();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_network_stats')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('network_stats')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('stat_date')}</th>\n                                        <th>{t('blockchain_height')}</th>\n                                        <th>{t('fil_per_tib')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {stats.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"3\" className=\"text-center\">{t('no_network_stats_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        stats.map((stat, index) => (\n                                            <tr key={`${stat.stat_date}`}>\n                                                <td>{new Date(stat.stat_date).toLocaleDateString()}</td>\n                                                <td>{stat.blockchain_height ? stat.blockchain_height.toLocaleString() : '-'}</td>\n                                                <td>{stat.fil_per_tib ? Number(stat.fil_per_tib).toFixed(8) : '0'} FIL/TiB</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default NetworkStats;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,KAAK,CAAEC,QAAQ,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC3B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC;AACxB;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE7C,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACzD,CAAC,IAAM,CACHT,QAAQ,CAACK,IAAI,CAAC,CAClB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,UAAU,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAqB,QAAA,CAAMjB,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CAClD,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAA8B,QAAA,eACNrB,IAAA,OAAIsB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEjB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC9CJ,IAAA,CAACR,GAAG,EAAA6B,QAAA,cACArB,IAAA,CAACP,GAAG,EAAA4B,QAAA,cACArB,IAAA,CAACN,IAAI,EAAA2B,QAAA,cACDrB,IAAA,CAACN,IAAI,CAAC6B,IAAI,EAAAF,QAAA,cACNnB,KAAA,CAACP,KAAK,EAAC6B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCrB,IAAA,UAAAqB,QAAA,cACInB,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,EAC3B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAqB,QAAA,CACKhB,KAAK,CAACuB,MAAM,GAAK,CAAC,cACf5B,IAAA,OAAAqB,QAAA,cACIrB,IAAA,OAAI6B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEjB,CAAC,CAAC,4BAA4B,CAAC,CAAK,CAAC,CAC9E,CAAC,CAELC,KAAK,CAACyB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAClB9B,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAK,GAAI,CAAAY,IAAI,CAACF,IAAI,CAACG,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAK,CAAC,cACxDnC,IAAA,OAAAqB,QAAA,CAAKU,IAAI,CAACK,iBAAiB,CAAGL,IAAI,CAACK,iBAAiB,CAACC,cAAc,CAAC,CAAC,CAAG,GAAG,CAAK,CAAC,cACjFnC,KAAA,OAAAmB,QAAA,EAAKU,IAAI,CAACO,WAAW,CAAGC,MAAM,CAACR,IAAI,CAACO,WAAW,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,UAAQ,EAAI,CAAC,GAH1E,GAAGT,IAAI,CAACG,SAAS,EAItB,CACP,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAA/B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}