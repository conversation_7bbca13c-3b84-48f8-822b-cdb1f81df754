{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge,ProgressBar}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OrderDistributions=()=>{const{t}=useTranslation();const[distributions,setDistributions]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchDistributions=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch order distributions with related information\nconst{data,error}=await supabase.from('order_distributions').select(`\n                    id,\n                    share_amount,\n                    reward_amount,\n                    fee_amount,\n                    progress,\n                    created_at,\n                    distribution_batches (\n                        id,\n                        currency_code,\n                        batch_amount,\n                        per_share_amount,\n                        status,\n                        distributed_at,\n                        products (\n                            name,\n                            category\n                        )\n                    ),\n                    orders (\n                        id,\n                        cid,\n                        shares,\n                        products (\n                            name,\n                            category\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    )\n                `).order('created_at',{ascending:false});if(error){console.error('Error fetching order distributions:',error);}else{setDistributions(data);}setLoading(false);};fetchDistributions();},[]);const getStatusBadge=status=>{switch(status){case'completed':return/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:t('completed')});case'pending':return/*#__PURE__*/_jsx(Badge,{bg:\"warning\",children:t('pending')});case'processing':return/*#__PURE__*/_jsx(Badge,{bg:\"info\",children:t('processing')});default:return/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",children:status||'-'});}};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_order_distributions')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('order_distributions')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('distribution_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('batch_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('order_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('customer')}),/*#__PURE__*/_jsx(\"th\",{children:t('product_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('currency_code')}),/*#__PURE__*/_jsx(\"th\",{children:t('share_amount')}),/*#__PURE__*/_jsx(\"th\",{children:t('reward_amount')}),/*#__PURE__*/_jsx(\"th\",{children:t('fee_amount')}),/*#__PURE__*/_jsx(\"th\",{children:t('progress')}),/*#__PURE__*/_jsx(\"th\",{children:t('batch_status')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:distributions.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"12\",className:\"text-center\",children:t('no_order_distributions_available')})}):distributions.map(distribution=>{var _distribution$distrib,_distribution$orders,_distribution$orders2,_distribution$custome,_distribution$custome2,_distribution$custome3,_distribution$orders3,_distribution$orders4,_distribution$distrib2,_distribution$distrib3,_distribution$distrib4,_distribution$share_a,_distribution$reward_,_distribution$fee_amo,_distribution$distrib5;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:distribution.id}),/*#__PURE__*/_jsx(\"td\",{children:((_distribution$distrib=distribution.distribution_batches)===null||_distribution$distrib===void 0?void 0:_distribution$distrib.id)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_distribution$orders=distribution.orders)===null||_distribution$orders===void 0?void 0:_distribution$orders.cid)||((_distribution$orders2=distribution.orders)===null||_distribution$orders2===void 0?void 0:_distribution$orders2.id)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:((_distribution$custome=distribution.customer_profiles)===null||_distribution$custome===void 0?void 0:_distribution$custome.real_name)||'-'}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:((_distribution$custome2=distribution.customer_profiles)===null||_distribution$custome2===void 0?void 0:(_distribution$custome3=_distribution$custome2.users)===null||_distribution$custome3===void 0?void 0:_distribution$custome3.email)||'-'})]})}),/*#__PURE__*/_jsx(\"td\",{children:((_distribution$orders3=distribution.orders)===null||_distribution$orders3===void 0?void 0:(_distribution$orders4=_distribution$orders3.products)===null||_distribution$orders4===void 0?void 0:_distribution$orders4.name)||((_distribution$distrib2=distribution.distribution_batches)===null||_distribution$distrib2===void 0?void 0:(_distribution$distrib3=_distribution$distrib2.products)===null||_distribution$distrib3===void 0?void 0:_distribution$distrib3.name)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_distribution$distrib4=distribution.distribution_batches)===null||_distribution$distrib4===void 0?void 0:_distribution$distrib4.currency_code)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_distribution$share_a=distribution.share_amount)===null||_distribution$share_a===void 0?void 0:_distribution$share_a.toFixed(2))||'0.00'}),/*#__PURE__*/_jsx(\"td\",{children:((_distribution$reward_=distribution.reward_amount)===null||_distribution$reward_===void 0?void 0:_distribution$reward_.toFixed(6))||'0.000000'}),/*#__PURE__*/_jsx(\"td\",{children:((_distribution$fee_amo=distribution.fee_amount)===null||_distribution$fee_amo===void 0?void 0:_distribution$fee_amo.toFixed(6))||'0.000000'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(ProgressBar,{now:distribution.progress*100,label:`${(distribution.progress*100).toFixed(1)}%`,style:{minWidth:'100px'}})})}),/*#__PURE__*/_jsx(\"td\",{children:getStatusBadge((_distribution$distrib5=distribution.distribution_batches)===null||_distribution$distrib5===void 0?void 0:_distribution$distrib5.status)}),/*#__PURE__*/_jsx(\"td\",{children:new Date(distribution.created_at).toLocaleString()})]},distribution.id);})})]})})})})})]});};export default OrderDistributions;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "ProgressBar", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "OrderDistributions", "t", "distributions", "setDistributions", "loading", "setLoading", "fetchDistributions", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "getStatusBadge", "status", "bg", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "distribution", "_distribution$distrib", "_distribution$orders", "_distribution$orders2", "_distribution$custome", "_distribution$custome2", "_distribution$custome3", "_distribution$orders3", "_distribution$orders4", "_distribution$distrib2", "_distribution$distrib3", "_distribution$distrib4", "_distribution$share_a", "_distribution$reward_", "_distribution$fee_amo", "_distribution$distrib5", "id", "distribution_batches", "orders", "cid", "customer_profiles", "real_name", "users", "email", "products", "name", "currency_code", "share_amount", "toFixed", "reward_amount", "fee_amount", "now", "progress", "label", "style", "min<PERSON><PERSON><PERSON>", "Date", "created_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/OrderDistributions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge, ProgressBar } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst OrderDistributions = () => {\n    const { t } = useTranslation();\n    const [distributions, setDistributions] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchDistributions = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch order distributions with related information\n            const { data, error } = await supabase\n                .from('order_distributions')\n                .select(`\n                    id,\n                    share_amount,\n                    reward_amount,\n                    fee_amount,\n                    progress,\n                    created_at,\n                    distribution_batches (\n                        id,\n                        currency_code,\n                        batch_amount,\n                        per_share_amount,\n                        status,\n                        distributed_at,\n                        products (\n                            name,\n                            category\n                        )\n                    ),\n                    orders (\n                        id,\n                        cid,\n                        shares,\n                        products (\n                            name,\n                            category\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    )\n                `)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching order distributions:', error);\n            } else {\n                setDistributions(data);\n            }\n            setLoading(false);\n        };\n\n        fetchDistributions();\n    }, []);\n\n    const getStatusBadge = (status) => {\n        switch (status) {\n            case 'completed':\n                return <Badge bg=\"success\">{t('completed')}</Badge>;\n            case 'pending':\n                return <Badge bg=\"warning\">{t('pending')}</Badge>;\n            case 'processing':\n                return <Badge bg=\"info\">{t('processing')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{status || '-'}</Badge>;\n        }\n    };\n\n    if (loading) {\n        return <div>{t('loading_order_distributions')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('order_distributions')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('distribution_id')}</th>\n                                        <th>{t('batch_id')}</th>\n                                        <th>{t('order_id')}</th>\n                                        <th>{t('customer')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('currency_code')}</th>\n                                        <th>{t('share_amount')}</th>\n                                        <th>{t('reward_amount')}</th>\n                                        <th>{t('fee_amount')}</th>\n                                        <th>{t('progress')}</th>\n                                        <th>{t('batch_status')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {distributions.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"12\" className=\"text-center\">{t('no_order_distributions_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        distributions.map(distribution => (\n                                            <tr key={distribution.id}>\n                                                <td>{distribution.id}</td>\n                                                <td>{distribution.distribution_batches?.id || '-'}</td>\n                                                <td>{distribution.orders?.cid || distribution.orders?.id || '-'}</td>\n                                                <td>\n                                                    <div>\n                                                        <div>{distribution.customer_profiles?.real_name || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {distribution.customer_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>\n                                                    {distribution.orders?.products?.name || \n                                                     distribution.distribution_batches?.products?.name || '-'}\n                                                </td>\n                                                <td>{distribution.distribution_batches?.currency_code || '-'}</td>\n                                                <td>{distribution.share_amount?.toFixed(2) || '0.00'}</td>\n                                                <td>{distribution.reward_amount?.toFixed(6) || '0.000000'}</td>\n                                                <td>{distribution.fee_amount?.toFixed(6) || '0.000000'}</td>\n                                                <td>\n                                                    <div>\n                                                        <ProgressBar \n                                                            now={distribution.progress * 100} \n                                                            label={`${(distribution.progress * 100).toFixed(1)}%`}\n                                                            style={{ minWidth: '100px' }}\n                                                        />\n                                                    </div>\n                                                </td>\n                                                <td>{getStatusBadge(distribution.distribution_batches?.status)}</td>\n                                                <td>{new Date(distribution.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default OrderDistributions;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,WAAW,KAAQ,iBAAiB,CACtF,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,aAAa,CAAEC,gBAAgB,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAoB,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACnC,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC/D,CAAC,IAAM,CACHT,gBAAgB,CAACK,IAAI,CAAC,CAC1B,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,kBAAkB,CAAC,CAAC,CACxB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAY,cAAc,CAAIC,MAAM,EAAK,CAC/B,OAAQA,MAAM,EACV,IAAK,WAAW,CACZ,mBAAOtB,IAAA,CAACL,KAAK,EAAC4B,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAEpB,CAAC,CAAC,WAAW,CAAC,CAAQ,CAAC,CACvD,IAAK,SAAS,CACV,mBAAOJ,IAAA,CAACL,KAAK,EAAC4B,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAEpB,CAAC,CAAC,SAAS,CAAC,CAAQ,CAAC,CACrD,IAAK,YAAY,CACb,mBAAOJ,IAAA,CAACL,KAAK,EAAC4B,EAAE,CAAC,MAAM,CAAAC,QAAA,CAAEpB,CAAC,CAAC,YAAY,CAAC,CAAQ,CAAC,CACrD,QACI,mBAAOJ,IAAA,CAACL,KAAK,EAAC4B,EAAE,CAAC,WAAW,CAAAC,QAAA,CAAEF,MAAM,EAAI,GAAG,CAAQ,CAAC,CAC5D,CACJ,CAAC,CAED,GAAIf,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAwB,QAAA,CAAMpB,CAAC,CAAC,6BAA6B,CAAC,CAAM,CAAC,CACxD,CAEA,mBACIF,KAAA,CAACZ,SAAS,EAAAkC,QAAA,eACNxB,IAAA,OAAIyB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEpB,CAAC,CAAC,qBAAqB,CAAC,CAAK,CAAC,cACpDJ,IAAA,CAACT,GAAG,EAAAiC,QAAA,cACAxB,IAAA,CAACR,GAAG,EAAAgC,QAAA,cACAxB,IAAA,CAACP,IAAI,EAAA+B,QAAA,cACDxB,IAAA,CAACP,IAAI,CAACiC,IAAI,EAAAF,QAAA,cACNtB,KAAA,CAACR,KAAK,EAACiC,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCxB,IAAA,UAAAwB,QAAA,cACItB,KAAA,OAAAsB,QAAA,eACIxB,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,cAC/BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,EAC1B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAwB,QAAA,CACKnB,aAAa,CAAC0B,MAAM,GAAK,CAAC,cACvB/B,IAAA,OAAAwB,QAAA,cACIxB,IAAA,OAAIgC,OAAO,CAAC,IAAI,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEpB,CAAC,CAAC,kCAAkC,CAAC,CAAK,CAAC,CACrF,CAAC,CAELC,aAAa,CAAC4B,GAAG,CAACC,YAAY,OAAAC,qBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,oBAC1B/C,KAAA,OAAAsB,QAAA,eACIxB,IAAA,OAAAwB,QAAA,CAAKU,YAAY,CAACgB,EAAE,CAAK,CAAC,cAC1BlD,IAAA,OAAAwB,QAAA,CAAK,EAAAW,qBAAA,CAAAD,YAAY,CAACiB,oBAAoB,UAAAhB,qBAAA,iBAAjCA,qBAAA,CAAmCe,EAAE,GAAI,GAAG,CAAK,CAAC,cACvDlD,IAAA,OAAAwB,QAAA,CAAK,EAAAY,oBAAA,CAAAF,YAAY,CAACkB,MAAM,UAAAhB,oBAAA,iBAAnBA,oBAAA,CAAqBiB,GAAG,KAAAhB,qBAAA,CAAIH,YAAY,CAACkB,MAAM,UAAAf,qBAAA,iBAAnBA,qBAAA,CAAqBa,EAAE,GAAI,GAAG,CAAK,CAAC,cACrElD,IAAA,OAAAwB,QAAA,cACItB,KAAA,QAAAsB,QAAA,eACIxB,IAAA,QAAAwB,QAAA,CAAM,EAAAc,qBAAA,CAAAJ,YAAY,CAACoB,iBAAiB,UAAAhB,qBAAA,iBAA9BA,qBAAA,CAAgCiB,SAAS,GAAI,GAAG,CAAM,CAAC,cAC7DvD,IAAA,UAAOyB,SAAS,CAAC,YAAY,CAAAD,QAAA,CACxB,EAAAe,sBAAA,CAAAL,YAAY,CAACoB,iBAAiB,UAAAf,sBAAA,kBAAAC,sBAAA,CAA9BD,sBAAA,CAAgCiB,KAAK,UAAAhB,sBAAA,iBAArCA,sBAAA,CAAuCiB,KAAK,GAAI,GAAG,CACjD,CAAC,EACP,CAAC,CACN,CAAC,cACLzD,IAAA,OAAAwB,QAAA,CACK,EAAAiB,qBAAA,CAAAP,YAAY,CAACkB,MAAM,UAAAX,qBAAA,kBAAAC,qBAAA,CAAnBD,qBAAA,CAAqBiB,QAAQ,UAAAhB,qBAAA,iBAA7BA,qBAAA,CAA+BiB,IAAI,KAAAhB,sBAAA,CACnCT,YAAY,CAACiB,oBAAoB,UAAAR,sBAAA,kBAAAC,sBAAA,CAAjCD,sBAAA,CAAmCe,QAAQ,UAAAd,sBAAA,iBAA3CA,sBAAA,CAA6Ce,IAAI,GAAI,GAAG,CACzD,CAAC,cACL3D,IAAA,OAAAwB,QAAA,CAAK,EAAAqB,sBAAA,CAAAX,YAAY,CAACiB,oBAAoB,UAAAN,sBAAA,iBAAjCA,sBAAA,CAAmCe,aAAa,GAAI,GAAG,CAAK,CAAC,cAClE5D,IAAA,OAAAwB,QAAA,CAAK,EAAAsB,qBAAA,CAAAZ,YAAY,CAAC2B,YAAY,UAAAf,qBAAA,iBAAzBA,qBAAA,CAA2BgB,OAAO,CAAC,CAAC,CAAC,GAAI,MAAM,CAAK,CAAC,cAC1D9D,IAAA,OAAAwB,QAAA,CAAK,EAAAuB,qBAAA,CAAAb,YAAY,CAAC6B,aAAa,UAAAhB,qBAAA,iBAA1BA,qBAAA,CAA4Be,OAAO,CAAC,CAAC,CAAC,GAAI,UAAU,CAAK,CAAC,cAC/D9D,IAAA,OAAAwB,QAAA,CAAK,EAAAwB,qBAAA,CAAAd,YAAY,CAAC8B,UAAU,UAAAhB,qBAAA,iBAAvBA,qBAAA,CAAyBc,OAAO,CAAC,CAAC,CAAC,GAAI,UAAU,CAAK,CAAC,cAC5D9D,IAAA,OAAAwB,QAAA,cACIxB,IAAA,QAAAwB,QAAA,cACIxB,IAAA,CAACJ,WAAW,EACRqE,GAAG,CAAE/B,YAAY,CAACgC,QAAQ,CAAG,GAAI,CACjCC,KAAK,CAAE,GAAG,CAACjC,YAAY,CAACgC,QAAQ,CAAG,GAAG,EAAEJ,OAAO,CAAC,CAAC,CAAC,GAAI,CACtDM,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAChC,CAAC,CACD,CAAC,CACN,CAAC,cACLrE,IAAA,OAAAwB,QAAA,CAAKH,cAAc,EAAA4B,sBAAA,CAACf,YAAY,CAACiB,oBAAoB,UAAAF,sBAAA,iBAAjCA,sBAAA,CAAmC3B,MAAM,CAAC,CAAK,CAAC,cACpEtB,IAAA,OAAAwB,QAAA,CAAK,GAAI,CAAA8C,IAAI,CAACpC,YAAY,CAACqC,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,GA9BxDtC,YAAY,CAACgB,EA+BlB,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAA/C,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}