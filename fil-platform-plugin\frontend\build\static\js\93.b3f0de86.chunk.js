"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[93],{9093:(e,a,r)=>{r.r(a),r.d(a,{default:()=>h});var s=r(5043),t=r(8628),l=r(1719),n=r(9853),i=r(4282),o=r(4117),d=r(4312),c=r(1283),u=r(579);const h=()=>{const{t:e}=(0,o.Bd)(),[a,r]=(0,s.useState)(""),[h,m]=(0,s.useState)(""),[g,p]=(0,s.useState)(""),[w,x]=(0,s.useState)(!1),f=(0,c.Zp)();return(0,u.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"80vh"},children:(0,u.jsx)(t.A,{style:{width:"400px"},children:(0,u.jsxs)(t.A.Body,{children:[(0,u.jsx)("h2",{className:"text-center mb-4",children:e("login")}),g&&(0,u.jsx)(l.A,{variant:"danger",children:g}),(0,u.jsxs)(n.A,{onSubmit:async r=>{r.preventDefault(),p(""),x(!0);try{var s;const e=(0,d.b)(),{error:r}=await e.auth.signInWithPassword({email:a,password:h});if(r)throw r;const{data:{user:t},error:l}=await e.auth.getUser();if(l)throw l;let n=null===t||void 0===t||null===(s=t.user_metadata)||void 0===s?void 0:s.role;if(!n){const{data:a,error:r}=await e.from("users").select("role").eq("id",t.id).single();if(r)throw r;n=a.role}switch(localStorage.setItem("user_role",n),n){case"maker":f("/maker",{replace:!0});break;case"agent":f("/agent",{replace:!0});break;default:f("/",{replace:!0})}}catch(t){console.error("Login Error:",t),p(t.message||e("login_failed"))}x(!1)},children:[(0,u.jsxs)(n.A.Group,{id:"email",className:"mb-3",children:[(0,u.jsx)(n.A.Label,{children:e("email_address")}),(0,u.jsx)(n.A.Control,{type:"email",required:!0,value:a,onChange:e=>r(e.target.value)})]}),(0,u.jsxs)(n.A.Group,{id:"password",className:"mb-3",children:[(0,u.jsx)(n.A.Label,{children:e("password")}),(0,u.jsx)(n.A.Control,{type:"password",required:!0,value:h,onChange:e=>m(e.target.value)})]}),(0,u.jsx)(i.A,{disabled:w,className:"w-100",type:"submit",children:e(w?"logging_in":"login")})]})]})})})}}}]);
//# sourceMappingURL=93.b3f0de86.chunk.js.map