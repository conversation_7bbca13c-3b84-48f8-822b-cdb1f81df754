{"ast": null, "code": "import React,{Suspense,useEffect,useState}from'react';import{initSupabase}from'./supabaseClient';import{HashRouter,Routes,Route,Link,Navigate,useLocation}from'react-router-dom';import{Container,Navbar,Nav,NavDropdown}from'react-bootstrap';import{useTranslation}from'react-i18next';import{FaTachometerAlt,FaHardHat,FaGlobe,FaCoins,FaChartBar}from'react-icons/fa';// Lazy load components for better performance\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=/*#__PURE__*/React.lazy(()=>import('./pages/LoginPage'));const CustomerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/customer/Dashboard'));const ProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/ProductListPage'));const OrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/OrderListPage'));const WalletPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/WalletPage'));const MyAccountPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyAccountPage'));const MyGainsPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyGainsPage'));const KycPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/KycPage'));const RecommendPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/RecommendPage'));const AgentDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Dashboard'));const AgentMemberList=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentMemberList'));const AgentProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentProductListPage'));const MakerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Dashboard'));const MakerProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerProductListPage'));const MakerOrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerOrderListPage'));const MakerFacilityListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerFacilities'));const MakerMinerListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerMiners'));const MinerEarnings=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerEarnings'));const MinerSnapshots=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerSnapshots'));const Transactions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Transactions'));const CoinBatches=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CoinBatches'));const NetworkStats=/*#__PURE__*/React.lazy(()=>import('./pages/maker/NetworkStats'));const CustomerAssets=/*#__PURE__*/React.lazy(()=>import('./pages/maker/CustomerAssets'));const OrderReports=/*#__PURE__*/React.lazy(()=>import('./pages/maker/OrderReports'));const OrderDistributions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/OrderDistributions'));function App(){const{t,i18n}=useTranslation();const[supabase,setSupabase]=useState(null);const[session,setSession]=useState(null);const[loading,setLoading]=useState(true);const role=localStorage.getItem('user_role');// 从 localStorage 读取用户角色\nuseEffect(()=>{const initialize=async()=>{const supa=await initSupabase();setSupabase(supa);const{data:{session}}=await supa.auth.getSession();setSession(session);supa.auth.onAuthStateChange((_event,newSession)=>{setSession(newSession);if(!newSession){localStorage.removeItem('user_role');}});setLoading(false);};initialize();},[]);const changeLanguage=lng=>{i18n.changeLanguage(lng);};// Debug: Log current URL and hash\nReact.useEffect(()=>{console.log('App mounted. Current URL:',window.location.href);console.log('Hash:',window.location.hash);},[]);// Require login to access protected pages\nconst RequireAuth=_ref=>{let{children}=_ref;const location=useLocation();if(!session){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}return children;};// Auto redirect from \"/\" based on role\nconst RoleRedirect=()=>{const role=localStorage.getItem('user_role');if(role==='maker')return/*#__PURE__*/_jsx(Navigate,{to:\"/maker\",replace:true});if(role==='agent')return/*#__PURE__*/_jsx(Navigate,{to:\"/agent\",replace:true});return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});// default to login page\n};return/*#__PURE__*/_jsx(HashRouter,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Navbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Navbar.Brand,{as:Link,to:\"/\",children:\"FIL Platform\"}),/*#__PURE__*/_jsx(Navbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(Navbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsxs(Nav,{className:\"me-auto\",children:[role==='maker'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaHardHat,{className:\"me-1\"}),t('miner_management')]}),id:\"maker-miner-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/miners\",children:t('miner_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/facilities\",children:t('facility_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/earnings\",children:t('earnings_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/transfers\",children:t('transfer_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/snapshots\",children:t('daily_snapshot')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),t('operations_management')]}),id:\"maker-operations-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/capacity\",children:t('capacity_expansion_request')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/orders\",children:t('maker_orders')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/manual-deposits\",children:t('manual_deposit')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaCoins,{className:\"me-1\"}),t('coin_management')]}),id:\"maker-coin-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/coin-batches\",children:t('coin_batches')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/network-stats\",children:t('network_stats')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaChartBar,{className:\"me-1\"}),t('report_management')]}),id:\"maker-report-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/customer-assets\",children:t('customer_assets')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/order-reports\",children:t('order_reports')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/order-distributions\",children:t('order_distributions')})]})]}),/*#__PURE__*/_jsxs(Nav.Link,{href:\"#/\",children:[/*#__PURE__*/_jsx(FaTachometerAlt,{className:\"me-1\"}),t('dashboard')]})]}),/*#__PURE__*/_jsx(Nav,{children:/*#__PURE__*/_jsxs(NavDropdown,{title:t('language'),id:\"basic-nav-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('ja'),children:\"\\u65E5\\u672C\\u8A9E\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('zh'),children:\"\\u4E2D\\u6587\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('en'),children:\"English\"})]})})]})]})}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(\"div\",{children:t('loading')}),children:loading?/*#__PURE__*/_jsx(\"div\",{children:t('initializing_platform')}):!supabase?/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-danger\",children:t('backend_connection_failed')}):/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RoleRedirect,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/customer\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/wallet\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(WalletPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyAccountPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my-gains\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyGainsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/kyc\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(KycPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/recommend\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RecommendPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/members\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentMemberList,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerOrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/facilities\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerFacilityListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/miners\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerMinerListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/earnings\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerEarnings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/transfers\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Transactions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/snapshots\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerSnapshots,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/coin-batches\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CoinBatches,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/network-stats\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(NetworkStats,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/customer-assets\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerAssets,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/order-reports\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/order-distributions\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderDistributions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})})})]})});}export default App;", "map": {"version": 3, "names": ["React", "Suspense", "useEffect", "useState", "initSupabase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Link", "Navigate", "useLocation", "Container", "<PERSON><PERSON><PERSON>", "Nav", "NavDropdown", "useTranslation", "FaTachometerAlt", "FaHardHat", "FaGlobe", "FaCoins", "FaChartBar", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "LoginPage", "lazy", "CustomerDashboard", "ProductListPage", "OrderListPage", "WalletPage", "MyAccountPage", "MyGainsPage", "KycPage", "RecommendPage", "AgentDashboard", "AgentMemberList", "AgentProductListPage", "MakerDashboard", "MakerProductListPage", "MakerOrderListPage", "MakerFacilityListPage", "MakerMinerListPage", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "MinerSnapshots", "Transactions", "CoinBatches", "NetworkStats", "CustomerAssets", "OrderReports", "OrderDistributions", "App", "t", "i18n", "supabase", "set<PERSON><PERSON><PERSON><PERSON>", "session", "setSession", "loading", "setLoading", "role", "localStorage", "getItem", "initialize", "supa", "data", "auth", "getSession", "onAuthStateChange", "_event", "newSession", "removeItem", "changeLanguage", "lng", "console", "log", "window", "location", "href", "hash", "RequireAuth", "_ref", "children", "to", "state", "from", "replace", "RoleRedirect", "bg", "variant", "expand", "Brand", "as", "Toggle", "Collapse", "id", "className", "title", "<PERSON><PERSON>", "onClick", "fallback", "path", "element"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/App.js"], "sourcesContent": ["import React, { Suspense, useEffect, useState } from 'react';\nimport { initSupabase } from './supabaseClient';\nimport {\n  HashRouter,\n  Routes,\n  Route,\n  Link,\n  Navigate,\n  useLocation,\n} from 'react-router-dom';\nimport { Container, Navbar, Nav, NavDropdown } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { FaTachometerAlt, FaHardHat, FaGlobe, FaCoins, FaChartBar } from 'react-icons/fa';\n\n// Lazy load components for better performance\nconst LoginPage = React.lazy(() => import('./pages/LoginPage'));\nconst CustomerDashboard = React.lazy(() => import('./pages/customer/Dashboard'));\nconst ProductListPage = React.lazy(() => import('./pages/customer/ProductListPage'));\nconst OrderListPage = React.lazy(() => import('./pages/customer/OrderListPage'));\nconst WalletPage = React.lazy(() => import('./pages/customer/WalletPage'));\nconst MyAccountPage = React.lazy(() => import('./pages/customer/MyAccountPage'));\nconst MyGainsPage = React.lazy(() => import('./pages/customer/MyGainsPage'));\nconst KycPage = React.lazy(() => import('./pages/customer/KycPage'));\nconst RecommendPage = React.lazy(() => import('./pages/customer/RecommendPage'));\nconst AgentDashboard = React.lazy(() => import('./pages/agent/Dashboard'));\nconst AgentMemberList = React.lazy(() => import('./pages/agent/AgentMemberList'));\nconst AgentProductListPage = React.lazy(() => import('./pages/agent/AgentProductListPage'));\nconst MakerDashboard = React.lazy(() => import('./pages/maker/Dashboard'));\nconst MakerProductListPage = React.lazy(() => import('./pages/maker/MakerProductListPage'));\nconst MakerOrderListPage = React.lazy(() => import('./pages/maker/MakerOrderListPage'));\nconst MakerFacilityListPage = React.lazy(() => import('./pages/maker/MakerFacilities'));\nconst MakerMinerListPage = React.lazy(() => import('./pages/maker/MakerMiners'));\nconst MinerEarnings = React.lazy(() => import('./pages/maker/MinerEarnings'));\nconst MinerSnapshots = React.lazy(() => import('./pages/maker/MinerSnapshots'));\nconst Transactions = React.lazy(() => import('./pages/maker/Transactions'));\nconst CoinBatches = React.lazy(() => import('./pages/maker/CoinBatches'));\nconst NetworkStats = React.lazy(() => import('./pages/maker/NetworkStats'));\nconst CustomerAssets = React.lazy(() => import('./pages/maker/CustomerAssets'));\nconst OrderReports = React.lazy(() => import('./pages/maker/OrderReports'));\nconst OrderDistributions = React.lazy(() => import('./pages/maker/OrderDistributions'));\n\nfunction App() {\n  const { t, i18n } = useTranslation();\n  const [supabase, setSupabase] = useState(null);\n  const [session, setSession] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const role = localStorage.getItem('user_role'); // 从 localStorage 读取用户角色\n\n  useEffect(() => {\n    const initialize = async () => {\n      const supa = await initSupabase();\n      setSupabase(supa);\n\n      const { data: { session } } = await supa.auth.getSession();\n      setSession(session);\n\n      supa.auth.onAuthStateChange((_event, newSession) => {\n        setSession(newSession);\n        if (!newSession) {\n          localStorage.removeItem('user_role');\n        }\n      });\n\n      setLoading(false);\n    };\n    initialize();\n  }, []);\n\n  const changeLanguage = (lng) => {\n    i18n.changeLanguage(lng);\n  };\n\n  // Debug: Log current URL and hash\n  React.useEffect(() => {\n    console.log('App mounted. Current URL:', window.location.href);\n    console.log('Hash:', window.location.hash);\n  }, []);\n\n  // Require login to access protected pages\n  const RequireAuth = ({ children }) => {\n    const location = useLocation();\n    if (!session) {\n      return <Navigate to=\"/login\" state={{ from: location }} replace />;\n    }\n    return children;\n  };\n\n  // Auto redirect from \"/\" based on role\n  const RoleRedirect = () => {\n    const role = localStorage.getItem('user_role');\n    if (role === 'maker') return <Navigate to=\"/maker\" replace />;\n    if (role === 'agent') return <Navigate to=\"/agent\" replace />;\n    return <Navigate to=\"/login\" replace />; // default to login page\n  };\n\n  return (\n    <HashRouter>\n      <div>\n        <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\">\n          <Container>\n            <Navbar.Brand as={Link} to=\"/\">FIL Platform</Navbar.Brand>\n            <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n            <Navbar.Collapse id=\"basic-navbar-nav\">\n              <Nav className=\"me-auto\">\n                {/* ===== ★ Maker 导航开始 ★ ===== */}\n                {role === 'maker' && (\n                  <>\n                    {/* Miner Management 下拉 */}\n                    <NavDropdown\n                      title={\n                        <>\n                          <FaHardHat className=\"me-1\" />\n                          {t('miner_management')}\n                        </>\n                      }\n                      id=\"maker-miner-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/miners\">\n                        {t('miner_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/facilities\">\n                        {t('facility_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/earnings\">\n                        {t('earnings_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/transfers\">\n                        {t('transfer_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/snapshots\">\n                        {t('daily_snapshot')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaGlobe className=\"me-1\" />\n                          {t('operations_management')}\n                        </>\n                      }\n                      id=\"maker-operations-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/capacity\">\n                        {t('capacity_expansion_request')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/orders\">\n                        {t('maker_orders')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/manual-deposits\">\n                        {t('manual_deposit')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaCoins className=\"me-1\" />\n                          {t('coin_management')}\n                        </>\n                      }\n                      id=\"maker-coin-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/coin-batches\">\n                        {t('coin_batches')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/network-stats\">\n                        {t('network_stats')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <NavDropdown title={\n                        <>\n                          <FaChartBar className=\"me-1\" />\n                          {t('report_management')}\n                        </>\n                      }\n                      id=\"maker-report-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/customer-assets\">\n                        {t('customer_assets')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/order-reports\">\n                        {t('order_reports')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/order-distributions\">\n                        {t('order_distributions')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n                  </>\n                )}\n                {/* ===== ★ Maker 导航结束 ★ ===== */}\n                <Nav.Link href=\"#/\">\n                  <FaTachometerAlt className=\"me-1\" />\n                  {t('dashboard')}\n                </Nav.Link>\n                {/* Add other nav links based on role later */}\n              </Nav>\n              <Nav>\n                <NavDropdown title={t('language')} id=\"basic-nav-dropdown\">\n                  <NavDropdown.Item onClick={() => changeLanguage('ja')}>日本語</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('zh')}>中文</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('en')}>English</NavDropdown.Item>\n                </NavDropdown>\n              </Nav>\n            </Navbar.Collapse>\n          </Container>\n        </Navbar>\n\n        <Container className=\"mt-4\">\n          <Suspense fallback={<div>{t('loading')}</div>}>\n            {loading ? (\n              <div>{t('initializing_platform')}</div>\n            ) : !supabase ? (\n              <div className=\"alert alert-danger\">{t('backend_connection_failed')}</div>\n            ) : (\n              <Routes>\n                {/* Public Route */}\n                <Route path=\"/login\" element={<LoginPage />} />\n\n                {/* Root path → redirect by role */}\n                <Route path=\"/\" element={<RequireAuth><RoleRedirect /></RequireAuth>} />\n\n                {/* Customer Routes */}\n                <Route path=\"/customer\" element={<RequireAuth><CustomerDashboard /></RequireAuth>} />\n                <Route path=\"/products\" element={<RequireAuth><ProductListPage /></RequireAuth>} />\n                <Route path=\"/orders\" element={<RequireAuth><OrderListPage /></RequireAuth>} />\n                <Route path=\"/wallet\" element={<RequireAuth><WalletPage /></RequireAuth>} />\n                <Route path=\"/my\" element={<RequireAuth><MyAccountPage /></RequireAuth>} />\n                <Route path=\"/my-gains\" element={<RequireAuth><MyGainsPage /></RequireAuth>} />\n                <Route path=\"/my/kyc\" element={<RequireAuth><KycPage /></RequireAuth>} />\n                <Route path=\"/my/recommend\" element={<RequireAuth><RecommendPage /></RequireAuth>} />\n\n                {/* Agent Routes */}\n                <Route path=\"/agent\" element={<RequireAuth><AgentDashboard /></RequireAuth>} />\n                <Route path=\"/agent/members\" element={<RequireAuth><AgentMemberList /></RequireAuth>} />\n                <Route path=\"/agent/products\" element={<RequireAuth><AgentProductListPage /></RequireAuth>} />\n\n                {/* Maker Routes */}\n                  <Route path=\"/maker\" element={<RequireAuth><MakerDashboard /></RequireAuth>} />\n                  <Route path=\"/maker/products\" element={<RequireAuth><MakerProductListPage /></RequireAuth>} />\n                  <Route path=\"/maker/orders\" element={<RequireAuth><MakerOrderListPage /></RequireAuth>} />\n                  <Route path=\"/maker/facilities\" element={<RequireAuth><MakerFacilityListPage /></RequireAuth>} />\n                  <Route path=\"/maker/miners\" element={<RequireAuth><MakerMinerListPage /></RequireAuth>} />\n                  <Route path=\"/maker/earnings\" element={<RequireAuth><MinerEarnings /></RequireAuth>} />\n                  <Route path=\"/maker/transfers\" element={<RequireAuth><Transactions /></RequireAuth>} />\n                  <Route path=\"/maker/snapshots\" element={<RequireAuth><MinerSnapshots /></RequireAuth>} />\n                  <Route path=\"/maker/coin-batches\" element={<RequireAuth><CoinBatches /></RequireAuth>} />\n                  <Route path=\"/maker/network-stats\" element={<RequireAuth><NetworkStats /></RequireAuth>} />\n                  <Route path=\"/maker/customer-assets\" element={<RequireAuth><CustomerAssets /></RequireAuth>} />\n                  <Route path=\"/maker/order-reports\" element={<RequireAuth><OrderReports /></RequireAuth>} />\n                  <Route path=\"/maker/order-distributions\" element={<RequireAuth><OrderDistributions /></RequireAuth>} />\n                \n                {/* Fallback */}\n                <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n              </Routes>\n            )}\n          </Suspense>\n        </Container>\n      </div>\n    </HashRouter>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC5D,OAASC,YAAY,KAAQ,kBAAkB,CAC/C,OACEC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,WAAW,KACN,kBAAkB,CACzB,OAASC,SAAS,CAAEC,MAAM,CAAEC,GAAG,CAAEC,WAAW,KAAQ,iBAAiB,CACrE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,eAAe,CAAEC,SAAS,CAAEC,OAAO,CAAEC,OAAO,CAAEC,UAAU,KAAQ,gBAAgB,CAEzF;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,SAAS,cAAG3B,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAC,iBAAiB,cAAG7B,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAAE,eAAe,cAAG9B,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACpF,KAAM,CAAAG,aAAa,cAAG/B,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAI,UAAU,cAAGhC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC1E,KAAM,CAAAK,aAAa,cAAGjC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAM,WAAW,cAAGlC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC5E,KAAM,CAAAO,OAAO,cAAGnC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACpE,KAAM,CAAAQ,aAAa,cAAGpC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAS,cAAc,cAAGrC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAU,eAAe,cAAGtC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACjF,KAAM,CAAAW,oBAAoB,cAAGvC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAY,cAAc,cAAGxC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAa,oBAAoB,cAAGzC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAc,kBAAkB,cAAG1C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACvF,KAAM,CAAAe,qBAAqB,cAAG3C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACvF,KAAM,CAAAgB,kBAAkB,cAAG5C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAChF,KAAM,CAAAiB,aAAa,cAAG7C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC7E,KAAM,CAAAkB,cAAc,cAAG9C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAmB,YAAY,cAAG/C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAoB,WAAW,cAAGhD,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CACzE,KAAM,CAAAqB,YAAY,cAAGjD,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAsB,cAAc,cAAGlD,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAuB,YAAY,cAAGnD,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAC3E,KAAM,CAAAwB,kBAAkB,cAAGpD,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CAEvF,QAAS,CAAAyB,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGxC,cAAc,CAAC,CAAC,CACpC,KAAM,CAACyC,QAAQ,CAAEC,WAAW,CAAC,CAAGtD,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACuD,OAAO,CAAEC,UAAU,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACyD,OAAO,CAAEC,UAAU,CAAC,CAAG1D,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAA2D,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAE;AAEhD9D,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+D,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAA9D,YAAY,CAAC,CAAC,CACjCqD,WAAW,CAACS,IAAI,CAAC,CAEjB,KAAM,CAAEC,IAAI,CAAE,CAAET,OAAQ,CAAE,CAAC,CAAG,KAAM,CAAAQ,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,CAAC,CAC1DV,UAAU,CAACD,OAAO,CAAC,CAEnBQ,IAAI,CAACE,IAAI,CAACE,iBAAiB,CAAC,CAACC,MAAM,CAAEC,UAAU,GAAK,CAClDb,UAAU,CAACa,UAAU,CAAC,CACtB,GAAI,CAACA,UAAU,CAAE,CACfT,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC,CACtC,CACF,CAAC,CAAC,CAEFZ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CACDI,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,cAAc,CAAIC,GAAG,EAAK,CAC9BpB,IAAI,CAACmB,cAAc,CAACC,GAAG,CAAC,CAC1B,CAAC,CAED;AACA3E,KAAK,CAACE,SAAS,CAAC,IAAM,CACpB0E,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAC9DJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEC,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAC,CAC5C,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC/B,KAAM,CAAAJ,QAAQ,CAAGrE,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACgD,OAAO,CAAE,CACZ,mBAAOpC,IAAA,CAACb,QAAQ,EAAC4E,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAER,QAAS,CAAE,CAACS,OAAO,MAAE,CAAC,CACpE,CACA,MAAO,CAAAJ,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAA3B,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC9C,GAAIF,IAAI,GAAK,OAAO,CAAE,mBAAOxC,IAAA,CAACb,QAAQ,EAAC4E,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,GAAI1B,IAAI,GAAK,OAAO,CAAE,mBAAOxC,IAAA,CAACb,QAAQ,EAAC4E,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,mBAAOlE,IAAA,CAACb,QAAQ,EAAC4E,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAAE;AAC3C,CAAC,CAED,mBACElE,IAAA,CAACjB,UAAU,EAAA+E,QAAA,cACT1D,KAAA,QAAA0D,QAAA,eACE9D,IAAA,CAACV,MAAM,EAAC8E,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAAR,QAAA,cAC1C1D,KAAA,CAACf,SAAS,EAAAyE,QAAA,eACR9D,IAAA,CAACV,MAAM,CAACiF,KAAK,EAACC,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,GAAG,CAAAD,QAAA,CAAC,cAAY,CAAc,CAAC,cAC1D9D,IAAA,CAACV,MAAM,CAACmF,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAClDrE,KAAA,CAACd,MAAM,CAACoF,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAb,QAAA,eACpC1D,KAAA,CAACb,GAAG,EAACqF,SAAS,CAAC,SAAS,CAAAd,QAAA,EAErBtB,IAAI,GAAK,OAAO,eACfpC,KAAA,CAAAF,SAAA,EAAA4D,QAAA,eAEE1D,KAAA,CAACZ,WAAW,EACVqF,KAAK,cACHzE,KAAA,CAAAF,SAAA,EAAA4D,QAAA,eACE9D,IAAA,CAACL,SAAS,EAACiF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7B5C,CAAC,CAAC,kBAAkB,CAAC,EACtB,CACH,CACD2C,EAAE,CAAC,sBAAsB,CAAAb,QAAA,eAEzB9D,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,YAAY,CAAC,CACA,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAC/C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9C9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEd5B,KAAA,CAACZ,WAAW,EAACqF,KAAK,cACdzE,KAAA,CAAAF,SAAA,EAAA4D,QAAA,eACE9D,IAAA,CAACJ,OAAO,EAACgF,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B5C,CAAC,CAAC,uBAAuB,CAAC,EAC3B,CACH,CACD2C,EAAE,CAAC,2BAA2B,CAAAb,QAAA,eAE9B9D,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,4BAA4B,CAAC,CAChB,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpD9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEd5B,KAAA,CAACZ,WAAW,EAACqF,KAAK,cACdzE,KAAA,CAAAF,SAAA,EAAA4D,QAAA,eACE9D,IAAA,CAACH,OAAO,EAAC+E,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B5C,CAAC,CAAC,iBAAiB,CAAC,EACrB,CACH,CACD2C,EAAE,CAAC,qBAAqB,CAAAb,QAAA,eAExB9D,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,qBAAqB,CAAAD,QAAA,CACjD9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClD9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,EACR,CAAC,cAEd5B,KAAA,CAACZ,WAAW,EAACqF,KAAK,cACdzE,KAAA,CAAAF,SAAA,EAAA4D,QAAA,eACE9D,IAAA,CAACF,UAAU,EAAC8E,SAAS,CAAC,MAAM,CAAE,CAAC,CAC9B5C,CAAC,CAAC,mBAAmB,CAAC,EACvB,CACH,CACD2C,EAAE,CAAC,uBAAuB,CAAAb,QAAA,eAE1B9D,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpD9B,CAAC,CAAC,iBAAiB,CAAC,CACL,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,sBAAsB,CAAAD,QAAA,CAClD9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnBhC,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACN,EAAE,CAAEtF,IAAK,CAAC6E,EAAE,CAAC,4BAA4B,CAAAD,QAAA,CACxD9B,CAAC,CAAC,qBAAqB,CAAC,CACT,CAAC,EACR,CAAC,EACd,CACH,cAED5B,KAAA,CAACb,GAAG,CAACL,IAAI,EAACwE,IAAI,CAAC,IAAI,CAAAI,QAAA,eACjB9D,IAAA,CAACN,eAAe,EAACkF,SAAS,CAAC,MAAM,CAAE,CAAC,CACnC5C,CAAC,CAAC,WAAW,CAAC,EACP,CAAC,EAER,CAAC,cACNhC,IAAA,CAACT,GAAG,EAAAuE,QAAA,cACF1D,KAAA,CAACZ,WAAW,EAACqF,KAAK,CAAE7C,CAAC,CAAC,UAAU,CAAE,CAAC2C,EAAE,CAAC,oBAAoB,CAAAb,QAAA,eACxD9D,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAM3B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,oBAAG,CAAkB,CAAC,cAC7E9D,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAM3B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,cAAE,CAAkB,CAAC,cAC5E9D,IAAA,CAACR,WAAW,CAACsF,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAM3B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,SAAO,CAAkB,CAAC,EACtE,CAAC,CACX,CAAC,EACS,CAAC,EACT,CAAC,CACN,CAAC,cAET9D,IAAA,CAACX,SAAS,EAACuF,SAAS,CAAC,MAAM,CAAAd,QAAA,cACzB9D,IAAA,CAACrB,QAAQ,EAACqG,QAAQ,cAAEhF,IAAA,QAAA8D,QAAA,CAAM9B,CAAC,CAAC,SAAS,CAAC,CAAM,CAAE,CAAA8B,QAAA,CAC3CxB,OAAO,cACNtC,IAAA,QAAA8D,QAAA,CAAM9B,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CACrC,CAACE,QAAQ,cACXlC,IAAA,QAAK4E,SAAS,CAAC,oBAAoB,CAAAd,QAAA,CAAE9B,CAAC,CAAC,2BAA2B,CAAC,CAAM,CAAC,cAE1E5B,KAAA,CAACpB,MAAM,EAAA8E,QAAA,eAEL9D,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAElF,IAAA,CAACK,SAAS,GAAE,CAAE,CAAE,CAAC,cAG/CL,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,GAAG,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACmE,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGxEnE,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACO,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACrFP,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACQ,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFR,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,SAAS,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACS,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/ET,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,SAAS,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACU,UAAU,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC5EV,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,KAAK,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACW,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3EX,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACY,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/EZ,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,SAAS,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACa,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzEb,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACc,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGrFd,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACe,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/Ef,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACgB,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACxFhB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACiB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAG5FjB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACkB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/ElB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACmB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC9FnB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACoB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FpB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACqB,qBAAqB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACjGrB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACsB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FtB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACuB,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFvB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACyB,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFzB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAACwB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFxB,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAAC0B,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzF1B,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAAC2B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F3B,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAAC4B,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/F5B,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAAC6B,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F7B,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,4BAA4B,CAACC,OAAO,cAAElF,IAAA,CAAC4D,WAAW,EAAAE,QAAA,cAAC9D,IAAA,CAAC8B,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGzG9B,IAAA,CAACf,KAAK,EAACgG,IAAI,CAAC,GAAG,CAACC,OAAO,cAAElF,IAAA,CAACb,QAAQ,EAAC4E,EAAE,CAAC,GAAG,CAACG,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CACT,CACO,CAAC,CACF,CAAC,EACT,CAAC,CACI,CAAC,CAEjB,CAEA,cAAe,CAAAnC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}