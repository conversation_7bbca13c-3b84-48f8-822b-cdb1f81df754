"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[738],{2738:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var a=r(5043),s=r(3519),i=r(8628),n=r(1719),d=r(9853),o=r(4282),l=r(4312),c=r(4117),u=r(579);const p=()=>{var e,t;const{t:r}=(0,c.Bd)(),[p,m]=(0,a.useState)(""),[g,_]=(0,a.useState)(""),[f,b]=(0,a.useState)(null),[h,x]=(0,a.useState)(null),[y,v]=(0,a.useState)("pending"),[j,k]=(0,a.useState)(!0),[A,w]=(0,a.useState)(!1),[C,S]=(0,a.useState)({type:"",text:""});(0,a.useEffect)(()=>{(async()=>{const e=(0,l.b)();if(!e)return;const{data:{user:t}}=await e.auth.getUser();if(!t)return void k(!1);const{data:a,error:s}=await e.from("customer_profiles").select("real_name, id_number, id_img_front, id_img_back, verify_status").eq("user_id",t.id).single();s&&"PGRST116"!==s.code?(console.error("Error fetching KYC status:",s),S({type:"danger",text:r("failed_to_load_kyc_status")})):a&&(m(a.real_name||""),_(a.id_number||""),b(a.id_img_front||null),x(a.id_img_back||null),v(a.verify_status||"pending")),k(!1)})()},[]);const N=(e,t)=>{e.target.files&&e.target.files[0]&&t(e.target.files[0])};return j?(0,u.jsx)("div",{children:r("loading_kyc_status")}):(0,u.jsxs)(s.A,{children:[(0,u.jsx)("h2",{className:"mb-4",children:r("kyc_verification")}),(0,u.jsx)(i.A,{children:(0,u.jsxs)(i.A.Body,{children:[C.text&&(0,u.jsx)(n.A,{variant:C.type,children:C.text}),"approved"===y&&(0,u.jsx)(n.A,{variant:"success",children:r("kyc_approved")}),"pending"===y&&(0,u.jsx)(n.A,{variant:"info",children:r("kyc_pending_review")}),"rejected"===y&&(0,u.jsx)(n.A,{variant:"danger",children:r("kyc_rejected")}),(0,u.jsxs)(d.A,{onSubmit:async e=>{e.preventDefault(),w(!0),S({type:"",text:""});const t=(0,l.b)();if(!t)return;const{data:{user:a}}=await t.auth.getUser();if(!a)return S({type:"danger",text:r("user_not_logged_in")}),void w(!1);try{let e=f,s=h;if(f instanceof File){const{data:r,error:s}=await t.storage.from("kyc-documents").upload(`${a.id}/front_${Date.now()}`,f,{cacheControl:"3600",upsert:!1});if(s)throw s;e=r.path}if(h instanceof File){const{data:e,error:r}=await t.storage.from("kyc-documents").upload(`${a.id}/back_${Date.now()}`,h,{cacheControl:"3600",upsert:!1});if(r)throw r;s=e.path}const{error:i}=await t.from("customer_profiles").upsert({user_id:a.id,real_name:p,id_number:g,id_img_front:e,id_img_back:s,verify_status:"pending"},{onConflict:"user_id"});if(i)throw i;S({type:"success",text:r("kyc_submit_success")}),v("pending")}catch(s){console.error("KYC submission error:",s),S({type:"danger",text:r("failed_to_submit_kyc")+": "+s.message})}w(!1)},children:[(0,u.jsxs)(d.A.Group,{className:"mb-3",children:[(0,u.jsx)(d.A.Label,{children:r("real_name")}),(0,u.jsx)(d.A.Control,{type:"text",value:p,onChange:e=>m(e.target.value),required:!0,disabled:"pending"===y||"approved"===y})]}),(0,u.jsxs)(d.A.Group,{className:"mb-3",children:[(0,u.jsx)(d.A.Label,{children:r("id_number")}),(0,u.jsx)(d.A.Control,{type:"text",value:g,onChange:e=>_(e.target.value),required:!0,disabled:"pending"===y||"approved"===y})]}),(0,u.jsxs)(d.A.Group,{className:"mb-3",children:[(0,u.jsx)(d.A.Label,{children:r("id_front")}),(0,u.jsx)(d.A.Control,{type:"file",onChange:e=>N(e,b),accept:"image/*",disabled:"pending"===y||"approved"===y}),f&&"string"===typeof f&&(0,u.jsx)("img",{src:null===(e=(0,l.b)())||void 0===e?void 0:e.storage.from("kyc-documents").getPublicUrl(f).data.publicUrl,alt:"ID Front",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,u.jsxs)(d.A.Group,{className:"mb-3",children:[(0,u.jsx)(d.A.Label,{children:r("id_back")}),(0,u.jsx)(d.A.Control,{type:"file",onChange:e=>N(e,x),accept:"image/*",disabled:"pending"===y||"approved"===y}),h&&"string"===typeof h&&(0,u.jsx)("img",{src:null===(t=(0,l.b)())||void 0===t?void 0:t.storage.from("kyc-documents").getPublicUrl(h).data.publicUrl,alt:"ID Back",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,u.jsx)(o.A,{variant:"primary",type:"submit",disabled:A||"pending"===y||"approved"===y,children:r(A?"submitting":"submit_review")})]})]})})]})}}}]);
//# sourceMappingURL=738.f9097e43.chunk.js.map