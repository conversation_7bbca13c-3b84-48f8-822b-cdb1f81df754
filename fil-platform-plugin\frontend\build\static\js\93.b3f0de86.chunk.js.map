{"version": 3, "file": "static/js/93.b3f0de86.chunk.js", "mappings": "6NAMA,MAyGA,EAzGkBA,KAChB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,KAClCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAASC,IAAcN,EAAAA,EAAAA,WAAS,GACjCO,GAAWC,EAAAA,EAAAA,MA2DjB,OACEC,EAAAA,EAAAA,KAAA,OACEC,UAAU,mDACVC,MAAO,CAAEC,UAAW,QAASC,UAE7BJ,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAACH,MAAO,CAAEI,MAAO,SAAUF,UAC9BG,EAAAA,EAAAA,MAACF,EAAAA,EAAKG,KAAI,CAAAJ,SAAA,EACRJ,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mBAAkBG,SAAEjB,EAAE,WACnCO,IAASM,EAAAA,EAAAA,KAACS,EAAAA,EAAK,CAACC,QAAQ,SAAQN,SAAEV,KACnCa,EAAAA,EAAAA,MAACI,EAAAA,EAAI,CAACC,SAlEOC,UACnBC,EAAEC,iBACFpB,EAAS,IACTE,GAAW,GAEX,IAAK,IAADmB,EACF,MAAMC,GAAWC,EAAAA,EAAAA,MAGTxB,MAAOyB,SAAoBF,EAASG,KAAKC,mBAAmB,CAClEhC,QACAG,aAEF,GAAI2B,EAAW,MAAMA,EAGrB,MACEG,MAAM,KAAEC,GACR7B,MAAO8B,SACCP,EAASG,KAAKK,UACxB,GAAID,EAAS,MAAMA,EAEnB,IAAIE,EAAW,OAAJH,QAAI,IAAJA,GAAmB,QAAfP,EAAJO,EAAMI,qBAAa,IAAAX,OAAf,EAAJA,EAAqBU,KAGhC,IAAKA,EAAM,CACT,MAAM,KAAEJ,EAAM5B,MAAOkC,SAAqBX,EACvCY,KAAK,SACLC,OAAO,QACPC,GAAG,KAAMR,EAAKS,IACdC,SACH,GAAIL,EAAY,MAAMA,EACtBF,EAAOJ,EAAKI,IACd,CAMA,OAHAQ,aAAaC,QAAQ,YAAaT,GAG1BA,GACN,IAAK,QACH5B,EAAS,SAAU,CAAEsC,SAAS,IAC9B,MACF,IAAK,QACHtC,EAAS,SAAU,CAAEsC,SAAS,IAC9B,MACF,QACEtC,EAAS,IAAK,CAAEsC,SAAS,IAE/B,CAAE,MAAOC,GACPC,QAAQ5C,MAAM,eAAgB2C,GAC9B1C,EAAS0C,EAAIE,SAAWpD,EAAE,gBAC5B,CAEAU,GAAW,IAYwBO,SAAA,EAC3BG,EAAAA,EAAAA,MAACI,EAAAA,EAAK6B,MAAK,CAACR,GAAG,QAAQ/B,UAAU,OAAMG,SAAA,EACrCJ,EAAAA,EAAAA,KAACW,EAAAA,EAAK8B,MAAK,CAAArC,SAAEjB,EAAE,oBACfa,EAAAA,EAAAA,KAACW,EAAAA,EAAK+B,QAAO,CACXC,KAAK,QACLC,UAAQ,EACRC,MAAOxD,EACPyD,SAAWhC,GAAMxB,EAASwB,EAAEiC,OAAOF,aAIvCtC,EAAAA,EAAAA,MAACI,EAAAA,EAAK6B,MAAK,CAACR,GAAG,WAAW/B,UAAU,OAAMG,SAAA,EACxCJ,EAAAA,EAAAA,KAACW,EAAAA,EAAK8B,MAAK,CAAArC,SAAEjB,EAAE,eACfa,EAAAA,EAAAA,KAACW,EAAAA,EAAK+B,QAAO,CACXC,KAAK,WACLC,UAAQ,EACRC,MAAOrD,EACPsD,SAAWhC,GAAMrB,EAAYqB,EAAEiC,OAAOF,aAI1C7C,EAAAA,EAAAA,KAACgD,EAAAA,EAAM,CAACC,SAAUrD,EAASK,UAAU,QAAQ0C,KAAK,SAAQvC,SAC7CjB,EAAVS,EAAY,aAAkB,qB", "sources": ["pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON>, Button, Card, Alert } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../supabaseClient';\nimport { useNavigate } from 'react-router-dom';\n\nconst LoginPage = () => {\n  const { t } = useTranslation();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const supabase = getSupabase();\n\n      /* ① 登录 */\n      const { error: signError } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n      if (signError) throw signError;\n\n      /* ② 取当前用户 & role */\n      const {\n        data: { user },\n        error: userErr,\n      } = await supabase.auth.getUser();\n      if (userErr) throw userErr;\n\n      let role = user?.user_metadata?.role;\n\n      // 如果 user_metadata 里没有 role，就去 public.users 表查询\n      if (!role) {\n        const { data, error: profileErr } = await supabase\n          .from('users')\n          .select('role')\n          .eq('id', user.id)\n          .single();\n        if (profileErr) throw profileErr;\n        role = data.role;\n      }\n\n      /* ③ 把 role 存到 localStorage，供前端使用 */\n      localStorage.setItem('user_role', role);\n\n      /* ④ 根据 role 重定向 */\n      switch (role) {\n        case 'maker':\n          navigate('/maker', { replace: true });\n          break;\n        case 'agent':\n          navigate('/agent', { replace: true });\n          break;\n        default:\n          navigate('/', { replace: true }); // customer\n      }\n    } catch (err) {\n      console.error('Login Error:', err);\n      setError(err.message || t('login_failed'));\n    }\n\n    setLoading(false);\n  };\n\n  return (\n    <div\n      className=\"d-flex justify-content-center align-items-center\"\n      style={{ minHeight: '80vh' }}\n    >\n      <Card style={{ width: '400px' }}>\n        <Card.Body>\n          <h2 className=\"text-center mb-4\">{t('login')}</h2>\n          {error && <Alert variant=\"danger\">{error}</Alert>}\n          <Form onSubmit={handleSubmit}>\n            <Form.Group id=\"email\" className=\"mb-3\">\n              <Form.Label>{t('email_address')}</Form.Label>\n              <Form.Control\n                type=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n              />\n            </Form.Group>\n\n            <Form.Group id=\"password\" className=\"mb-3\">\n              <Form.Label>{t('password')}</Form.Label>\n              <Form.Control\n                type=\"password\"\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n            </Form.Group>\n\n            <Button disabled={loading} className=\"w-100\" type=\"submit\">\n              {loading ? t('logging_in') : t('login')}\n            </Button>\n          </Form>\n        </Card.Body>\n      </Card>\n    </div>\n  );\n};\n\nexport default LoginPage;"], "names": ["LoginPage", "t", "useTranslation", "email", "setEmail", "useState", "password", "setPassword", "error", "setError", "loading", "setLoading", "navigate", "useNavigate", "_jsx", "className", "style", "minHeight", "children", "Card", "width", "_jsxs", "Body", "<PERSON><PERSON>", "variant", "Form", "onSubmit", "async", "e", "preventDefault", "_user$user_metadata", "supabase", "getSupabase", "signError", "auth", "signInWithPassword", "data", "user", "userErr", "getUser", "role", "user_metadata", "profileErr", "from", "select", "eq", "id", "single", "localStorage", "setItem", "replace", "err", "console", "message", "Group", "Label", "Control", "type", "required", "value", "onChange", "target", "<PERSON><PERSON>", "disabled"], "sourceRoot": ""}